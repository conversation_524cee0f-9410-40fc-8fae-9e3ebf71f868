#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Firefox 修复版自动化
跳过有问题的部分，专注于核心流程
"""

import sys
import os

# 设置UTF-8编码，解决Windows下的编码问题
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
import time
import random
from pathlib import Path
import requests
import os

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import FirefoxAutomation
from handlers import AugmentAuth, OneMailHandler

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import json
import re
from datetime import datetime, timedelta

def save_token_to_api(token_response, metadata):
    """保存令牌到 Token API"""
    try:
        # 从环境变量读取配置
        api_port = os.getenv('TOKEN_API_PORT', '9043')
        auth_password = os.getenv('AUTH_PASSWORD', 'your_secret_password_here_change_this')

        # API 端点
        api_url = f'http://localhost:{api_port}/api/tokens/save'

        # 生成 UTC+8 时间戳
        now = datetime.utcnow()
        utc8_time = now + timedelta(hours=8)
        created_timestamp = int(utc8_time.timestamp() * 1000)

        # 构建请求数据
        token_data = {
            'access_token': token_response.access_token,
            'tenant_url': token_response.tenant_url,
            'description': metadata.get('description', ''),
            'email_note': metadata.get('email', ''),
            'user_agent': metadata.get('user_agent', ''),
            'session_id': metadata.get('session_id', ''),
            'created_timestamp': created_timestamp
        }

        # 设置请求头
        headers = {
            'Authorization': f'Bearer {auth_password}',
            'Content-Type': 'application/json'
        }

        print(f'🌐 调用 Token API: {api_url}')
        print(f'📊 令牌数据: access_token={token_response.access_token[:20]}..., tenant_url={token_response.tenant_url}')

        # 发送请求
        response = requests.post(api_url, json=token_data, headers=headers, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token_id = result.get('tokenId')
                print(f'✅ 令牌已通过 API 保存，ID: {token_id}')
                print(f'🕐 API 响应时间: {result.get("timestamp")}')
                return token_id
            else:
                print(f'❌ API 返回失败: {result.get("message", "Unknown error")}')
                return None
        else:
            print(f'❌ API 请求失败: HTTP {response.status_code}')
            print(f'响应内容: {response.text}')
            return None

    except requests.exceptions.RequestException as e:
        print(f'❌ 网络请求失败: {e}')
        return None
    except Exception as e:
        print(f'❌ 保存令牌到 API 失败: {e}')
        return None

def capture_html_debug(driver, step_name, description=""):
    """捕获当前页面的 HTML 用于调试"""
    try:
        # 创建 html 目录
        html_dir = "html"
        os.makedirs(html_dir, exist_ok=True)

        # 生成文件名
        timestamp = datetime.now().strftime("%H%M%S")
        filename = f"{timestamp}_{step_name}.html"
        filepath = os.path.join(html_dir, filename)

        # 获取页面源码
        page_source = driver.page_source

        # 添加调试信息到 HTML
        debug_info = f"""
<!-- DEBUG INFO -->
<!-- Step: {step_name} -->
<!-- Description: {description} -->
<!-- URL: {driver.current_url} -->
<!-- Timestamp: {datetime.now().isoformat()} -->
<!-- END DEBUG INFO -->

"""

        # 保存 HTML
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(debug_info + page_source)

        print(f'📄 HTML 已保存: {filepath}')
        return filepath

    except Exception as e:
        print(f'⚠️ HTML 捕获失败: {e}')
        return None

def extract_authorization_code_firefox(driver):
    """提取授权码 - Firefox 版本"""
    try:
        print('🔍 查找 Copy 按钮...')

        # 策略1: 劫持 clipboard.writeText 并点击按钮
        try:
            result = driver.execute_script("""
                try {
                    window.__authCopied = null;
                    const btn = document.getElementById('copyButton') || Array.from(document.querySelectorAll('button')).find(b => (b.textContent||'').toLowerCase().includes('copy'));
                    if (!btn) return { found: false };

                    // 尝试覆盖 clipboard.writeText
                    let installed = false;
                    try {
                        const original = navigator.clipboard.writeText;
                        Object.defineProperty(navigator.clipboard, 'writeText', {
                            configurable: true,
                            writable: true,
                            value: (text) => {
                                window.__authCopied = text;
                                return Promise.resolve();
                            }
                        });
                        installed = true;
                    } catch (e) {
                        // 退而求其次：在捕获阶段拦截点击
                        btn.addEventListener('click', function () {
                            try {
                                const scripts = Array.from(document.querySelectorAll('script'));
                                for (const s of scripts) {
                                    const c = s.textContent || s.innerHTML || '';
                                    const m = c.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                                    if (m) {
                                        try {
                                            const obj = (0, eval)('(' + m[1] + ')');
                                            if (obj && obj.code && obj.state && obj.tenant_url) {
                                                window.__authCopied = JSON.stringify(obj);
                                                break;
                                            }
                                        } catch {}
                                    }
                                }
                            } catch {}
                        }, { capture: true, once: true });
                    }

                    btn.click();
                    return { found: true, installed: installed };
                } catch (e) {
                    return { found: false, error: e.message };
                }
            """)

            if result.get('found'):
                print('✅ 找到并点击了 Copy 按钮')
                time.sleep(1)  # 等待执行

                # 获取拦截的内容
                captured = driver.execute_script("return window.__authCopied")
                if captured and captured.strip():
                    print(f'📋 通过拦截获取授权码: {captured[:100]}...')
                    return captured.strip()
        except Exception as e:
            print(f'⚠️ 策略1失败: {e}')

        # 策略2: 尝试使用 pyperclip 读取剪贴板
        try:
            # 先找到并点击 Copy 按钮
            copy_buttons = driver.find_elements(By.XPATH, "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'copy')]")
            if copy_buttons:
                copy_buttons[0].click()
                print('✅ 点击了 Copy 按钮')
                time.sleep(1)

                import pyperclip
                clipboard_content = pyperclip.paste()
                if clipboard_content and clipboard_content.strip():
                    print(f'📋 从剪贴板获取授权码: {clipboard_content[:100]}...')
                    return clipboard_content.strip()
        except Exception as e:
            print(f'⚠️ 策略2失败: {e}')

        # 策略3: 从页面JavaScript中提取
        try:
            auth_data = driver.execute_script("""
                try {
                    const scripts = Array.from(document.querySelectorAll('script'));
                    for (let i = 0; i < scripts.length; i++) {
                        const content = scripts[i].textContent || scripts[i].innerHTML || '';
                        const dataMatch = content.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                        if (dataMatch) {
                            const objTxt = dataMatch[1];
                            try {
                                const obj = (0, eval)('(' + objTxt + ')');
                                if (obj && obj.code && obj.state && obj.tenant_url) {
                                    return JSON.stringify(obj);
                                }
                            } catch {}
                        }
                    }
                    return null;
                } catch {
                    return null;
                }
            """)

            if auth_data:
                print(f'🎯 从页面JavaScript提取授权码: {auth_data[:100]}...')
                return auth_data
        except Exception as e:
            print(f'⚠️ 策略3失败: {e}')

        # 策略4: 从页面HTML内容提取
        try:
            page_source = driver.page_source

            # 尝试匹配 let data = {...}
            auth_code_match = re.search(r'let\s+data\s*=\s*(\{[\s\S]*?\})', page_source)
            if auth_code_match:
                obj_text = auth_code_match.group(1)
                code_match = re.search(r'code:\s*["\']([^"\']+)["\']', obj_text)
                state_match = re.search(r'state:\s*["\']([^"\']+)["\']', obj_text)
                tenant_match = re.search(r'tenant_url:\s*["\']([^"\']+)["\']', obj_text)

                if code_match and state_match and tenant_match:
                    result = {
                        'code': code_match.group(1),
                        'state': state_match.group(1),
                        'tenant_url': tenant_match.group(1)
                    }
                    auth_data = json.dumps(result)
                    print(f'📄 从页面内容提取授权码: {auth_data[:100]}...')
                    return auth_data
        except Exception as e:
            print(f'⚠️ 策略4失败: {e}')

        print('❌ 所有策略都失败了')
        return None

    except Exception as e:
        print(f'❌ 授权码提取失败: {e}')
        return None

def run_firefox_fixed():
    """修复版 Firefox 自动化流程"""
    print('🦊 Firefox 修复版自动化流程')
    print('=' * 50)
    
    automation = None
    start_time = time.time()
    
    try:
        # 步骤1-8: 使用现有的成功流程
        print('🔧 步骤1-8: 执行到验证码页面...')
        automation = FirefoxAutomation()
        augment_auth = AugmentAuth()
        onemail_handler = OneMailHandler()
        
        # 生成授权URL和临时邮箱
        auth_url = augment_auth.generate_auth_url()
        temp_email = onemail_handler.generate_email()
        automation.temp_email = temp_email
        
        print(f'授权 URL: {auth_url}')
        print(f'临时邮箱: {temp_email}')
        
        # 启动浏览器并导航
        automation.init_browser()
        automation.navigate_to_page(auth_url)

        # 捕获初始页面
        capture_html_debug(automation.driver, "01_initial_page", "初始授权页面")
        
        # 输入邮箱
        automation.enter_email(temp_email)

        # 捕获邮箱输入后的页面
        capture_html_debug(automation.driver, "02_email_entered", f"邮箱输入完成: {temp_email}")
        
        # 处理验证码
        automation.handle_captcha()

        # 捕获验证码处理后的页面
        capture_html_debug(automation.driver, "03_captcha_solved", "Turnstile验证码解决完成")
        
        # 点击 Continue (使用简化版本)
        print('🔄 点击 Continue...')
        
        # 确保 token 同步
        automation.ensure_turnstile_token_in_form_firefox()
        automation.sync_captcha_hidden_input_firefox()
        
        # 简单的按钮点击
        continue_button = WebDriverWait(automation.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"], button[name="action"], input[type="submit"]'))
        )
        continue_button.click()
        time.sleep(5)

        # 捕获点击Continue后的页面
        capture_html_debug(automation.driver, "04_continue_clicked", "Continue按钮点击完成")
        
        # 检查是否到达验证码页面
        current_url = automation.driver.current_url
        print(f'当前 URL: {current_url}')
        
        if 'passwordless-email-challenge' in current_url:
            print('✅ 成功到达验证码页面！')

            # 捕获验证码页面
            capture_html_debug(automation.driver, "05_verification_page", "到达验证码输入页面")
            
            # 步骤9: 获取验证码
            print('📨 获取邮箱验证码...')
            
            # 等待邮件发送（缩短等待时间）
            wait_seconds = random.randint(3, 5)
            print(f'⏰ 等待邮件发送（{wait_seconds}秒）...')
            time.sleep(wait_seconds)
            
            # 获取验证码
            verification_code = onemail_handler.get_verification_code(temp_email, 2)
            
            if verification_code:
                print(f'✅ 获取到验证码: {verification_code}')
                
                # 步骤10: 输入验证码
                print('🔢 输入验证码...')
                
                code_input = WebDriverWait(automation.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="code"], input[id="code"]'))
                )
                
                code_input.clear()
                code_input.send_keys(verification_code)

                # 捕获验证码输入后的页面
                capture_html_debug(automation.driver, "06_code_entered", f"验证码输入完成: {verification_code}")
                
                # 步骤11: 点击验证码页面的 Continue
                print('🔄 点击验证码页面的 Continue...')
                
                verify_button = WebDriverWait(automation.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"], button[name="action"]'))
                )
                verify_button.click()
                time.sleep(5)

                # 捕获验证码提交后的页面
                capture_html_debug(automation.driver, "07_code_submitted", "验证码提交完成")
                
                # 步骤12: 检查最终结果
                final_url = automation.driver.current_url
                print(f'验证后 URL: {final_url}')

                # 捕获最终页面
                capture_html_debug(automation.driver, "08_final_page", f"最终页面: {final_url}")
                
                # 提取授权码 - 使用多种策略
                print('📋 提取授权码...')
                authorization_code = extract_authorization_code_firefox(automation.driver)

                if authorization_code:
                    print(f'✅ 授权码提取成功: {authorization_code[:100]}...')

                    # 捕获授权码提取后的页面
                    capture_html_debug(automation.driver, "09_auth_code_extracted", "授权码提取完成")
                
                if authorization_code:
                    # 步骤13: 完成 OAuth 流程
                    print('🔐 完成 OAuth 流程...')
                    
                    token_response = augment_auth.complete_oauth_flow(authorization_code)
                    
                    print('✅ 真实 API 调用成功！')
                    print(f'🔑 访问令牌: {token_response.access_token[:30] if token_response.access_token else "N/A"}...')
                    print(f'🏢 租户 URL: {token_response.tenant_url}')
                    
                    # 保存令牌到 API
                    token_id = save_token_to_api(token_response, {
                        'description': 'Firefox token from fixed automation',
                        'user_agent': 'firefox-fixed-automation',
                        'session_id': f'firefox_fixed_{int(time.time())}',
                        'email': temp_email
                    })

                    if token_id:
                        print(f'✅ 令牌已通过 API 保存，ID: {token_id}')
                    else:
                        print('❌ 令牌保存失败，但 OAuth 流程成功完成')
                    
                    duration = time.time() - start_time
                    print(f'\n🎉 Firefox 自动化完全成功！')
                    print(f'⏱️ 总耗时: {duration:.2f}秒')
                    print(f'📁 HTML 调试文件保存在: html/ 目录')
                    
                else:
                    print('❌ 未能提取到授权码')
            else:
                print('❌ 未能获取到验证码')
        else:
            print('❌ 未能到达验证码页面')
            print(f'当前页面: {current_url}')

            # 捕获错误页面
            capture_html_debug(automation.driver, "ERROR_not_verification_page", f"未到达验证码页面: {current_url}")
        
        # 保持浏览器打开以便检查
        input('\n按 Enter 键关闭浏览器...')
        
    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
    except Exception as e:
        print(f'\n❌ 自动化失败: {e}')
        import traceback
        traceback.print_exc()

        # 捕获错误时的页面
        if automation and automation.driver:
            try:
                capture_html_debug(automation.driver, "ERROR_exception", f"异常发生: {str(e)}")
            except:
                pass
    finally:
        if automation and automation.driver:
            try:
                automation.driver.quit()
                print('🧹 浏览器已关闭')
            except:
                pass
        
        print('👋 程序结束')

if __name__ == '__main__':
    run_firefox_fixed()
