#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Token API 调用功能
"""

import sys
import os
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent.parent / '.env')
    print('✅ 已加载 .env 文件')
except ImportError:
    print('⚠️ python-dotenv 未安装，使用系统环境变量')

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

def save_token_to_api(token_response, metadata):
    """保存令牌到 Token API"""
    try:
        # 从环境变量读取配置
        api_port = os.getenv('TOKEN_API_PORT', '9043')
        auth_password = os.getenv('AUTH_PASSWORD', 'your_secret_password_here_change_this')
        
        # API 端点
        api_url = f'http://localhost:{api_port}/api/tokens/save'
        
        # 生成 UTC+8 时间戳
        now = datetime.utcnow()
        utc8_time = now + timedelta(hours=8)
        created_timestamp = int(utc8_time.timestamp() * 1000)
        
        print(f'🕐 当前 UTC 时间: {now.isoformat()}')
        print(f'🕐 UTC+8 时间: {utc8_time.isoformat()}')
        print(f'🕐 时间戳 (毫秒): {created_timestamp}')
        
        # 构建请求数据
        token_data = {
            'access_token': token_response.access_token,
            'tenant_url': token_response.tenant_url,
            'description': metadata.get('description', ''),
            'email_note': metadata.get('email', ''),
            'user_agent': metadata.get('user_agent', ''),
            'session_id': metadata.get('session_id', ''),
            'created_timestamp': created_timestamp
        }
        
        # 设置请求头
        headers = {
            'Authorization': f'Bearer {auth_password}',
            'Content-Type': 'application/json'
        }
        
        print(f'🌐 调用 Token API: {api_url}')
        print(f'📊 令牌数据: access_token={token_response.access_token[:20]}..., tenant_url={token_response.tenant_url}')
        
        # 发送请求
        response = requests.post(api_url, json=token_data, headers=headers, timeout=10)
        
        print(f'📡 HTTP 状态码: {response.status_code}')
        print(f'📡 响应内容: {response.text}')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token_id = result.get('tokenId')
                print(f'✅ 令牌已通过 API 保存，ID: {token_id}')
                print(f'🕐 API 响应时间: {result.get("timestamp")}')
                return token_id
            else:
                print(f'❌ API 返回失败: {result.get("message", "Unknown error")}')
                return None
        else:
            print(f'❌ API 请求失败: HTTP {response.status_code}')
            print(f'响应内容: {response.text}')
            return None
            
    except requests.exceptions.RequestException as e:
        print(f'❌ 网络请求失败: {e}')
        return None
    except Exception as e:
        print(f'❌ 保存令牌到 API 失败: {e}')
        return None

class MockTokenResponse:
    """模拟令牌响应"""
    def __init__(self):
        self.access_token = "test_access_token_12345678901234567890"
        self.tenant_url = "https://test.api.augmentcode.com/"

def test_api_call():
    """测试 API 调用"""
    print('🧪 测试 Token API 调用功能')
    print('=' * 50)

    # 显示环境变量
    api_port = os.getenv('TOKEN_API_PORT', '9043')
    auth_password = os.getenv('AUTH_PASSWORD', 'your_secret_password_here_change_this')
    print(f'🔧 TOKEN_API_PORT: {api_port}')
    print(f'🔧 AUTH_PASSWORD: {auth_password[:10]}...')
    print()
    
    # 创建模拟数据
    token_response = MockTokenResponse()
    metadata = {
        'description': 'Test token from API call test',
        'user_agent': 'test-api-call',
        'session_id': f'test_session_{int(time.time())}',
        'email': '<EMAIL>'
    }
    
    # 测试 API 调用
    token_id = save_token_to_api(token_response, metadata)
    
    if token_id:
        print(f'\n🎉 测试成功！令牌 ID: {token_id}')
    else:
        print(f'\n❌ 测试失败！')

if __name__ == '__main__':
    test_api_call()
